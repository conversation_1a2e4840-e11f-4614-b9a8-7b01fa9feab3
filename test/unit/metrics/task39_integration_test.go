// Package metrics provides development-appropriate tests for Task 39 - Metrics Aggregation Logic
package metrics

import (
	"testing"
	"time"

	"neuralmetergo/internal/metrics"
)

// TestTask39_BasicStatisticalFunctions tests the basic statistical functions (Subtask 39.1)
func TestTask39_BasicStatisticalFunctions(t *testing.T) {
	t.Log("Testing Task 39.1 - Basic Statistical Functions")

	// Test data - small dataset for development
	testData := []float64{1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0}

	// Test Mean
	mean, err := metrics.Mean(testData)
	if err != nil {
		t.<PERSON>rrorf("Mean calculation failed: %v", err)
	}
	expectedMean := 5.5
	if mean != expectedMean {
		t.<PERSON>("Expected mean %.2f, got %.2f", expectedMean, mean)
	}

	// Test Median
	median, err := metrics.Median(testData)
	if err != nil {
		t.<PERSON><PERSON>rf("Median calculation failed: %v", err)
	}
	expectedMedian := 5.5
	if median != expectedMedian {
		t.Errorf("Expected median %.2f, got %.2f", expectedMedian, median)
	}

	// Test Min/Max
	min, err := metrics.Min(testData)
	if err != nil || min != 1.0 {
		t.Errorf("Expected min 1.0, got %.2f (error: %v)", min, err)
	}

	max, err := metrics.Max(testData)
	if err != nil || max != 10.0 {
		t.Errorf("Expected max 10.0, got %.2f (error: %v)", max, err)
	}

	// Test Percentiles
	p95, err := metrics.Percentile(testData, 0.95)
	if err != nil {
		t.Errorf("P95 calculation failed: %v", err)
	}
	if p95 < 9.0 || p95 > 10.0 {
		t.Errorf("P95 %.2f seems out of expected range [9.0, 10.0]", p95)
	}

	t.Log("✅ Basic statistical functions working correctly")
}

// TestTask39_PercentileCalculations tests the percentile calculation algorithms (Subtask 39.2)
func TestTask39_PercentileCalculations(t *testing.T) {
	t.Log("Testing Task 39.2 - Percentile Calculation Algorithms")

	// Test with small dataset for development
	testData := []float64{10, 20, 30, 40, 50, 60, 70, 80, 90, 100}

	// Test T-Digest calculator (development-friendly configuration)
	tdigest, err := metrics.NewPercentileCalculator(metrics.AlgorithmTDigest, 50.0) // Small compression
	if err != nil {
		t.Fatalf("Failed to create T-Digest calculator: %v", err)
	}

	for _, value := range testData {
		tdigest.Add(value)
	}

	p50 := tdigest.Quantile(0.5)
	p95 := tdigest.Quantile(0.95)

	if p50 < 40 || p50 > 60 {
		t.Errorf("T-Digest P50 %.2f seems out of expected range [40, 60]", p50)
	}
	if p95 < 90 || p95 > 100 {
		t.Errorf("T-Digest P95 %.2f seems out of expected range [90, 100]", p95)
	}

	// Test P² algorithm
	p2, err := metrics.NewPercentileCalculator(metrics.AlgorithmP2, 0.95)
	if err != nil {
		t.Fatalf("Failed to create P² calculator: %v", err)
	}

	for _, value := range testData {
		p2.Add(value)
	}

	p95_p2 := p2.Quantile(0.95)
	if p95_p2 < 85 || p95_p2 > 100 {
		t.Errorf("P² P95 %.2f seems out of expected range [85, 100]", p95_p2)
	}

	t.Logf("✅ Percentile calculations working: T-Digest P95=%.2f, P² P95=%.2f", p95, p95_p2)
}

// TestTask39_SlidingWindowAggregation tests the sliding window logic (Subtask 39.3)
func TestTask39_SlidingWindowAggregation(t *testing.T) {
	t.Log("Testing Task 39.3 - Sliding Window Aggregation Logic")

	// Test count-based sliding window (small for development)
	countConfig := metrics.SlidingWindowConfig{
		Type:        metrics.WindowTypeCount,
		Capacity:    5, // Very small for development testing
		EnableStats: true,
		EnableAsync: false, // Synchronous for predictable testing
	}

	countWindow, err := metrics.NewSlidingWindow(countConfig)
	if err != nil {
		t.Fatalf("Failed to create count-based sliding window: %v", err)
	}
	defer countWindow.Close()

	// Add test values
	testValues := []float64{10, 20, 30, 40, 50, 60, 70}
	for _, value := range testValues {
		if err := countWindow.Add(value); err != nil {
			t.Errorf("Failed to add value %.2f: %v", value, err)
		}
	}

	// Verify window size (should be limited to capacity)
	if countWindow.Size() != 5 {
		t.Errorf("Expected window size 5, got %d", countWindow.Size())
	}

	// Test statistics
	stats := countWindow.GetStatistics()
	if stats.Count != 5 {
		t.Errorf("Expected stats count 5, got %d", stats.Count)
	}

	// Test time-based sliding window (small duration for development)
	timeConfig := metrics.SlidingWindowConfig{
		Type:        metrics.WindowTypeTime,
		Duration:    2 * time.Second, // Very short for development testing
		EnableStats: true,
		EnableAsync: false, // Synchronous for testing
	}

	timeWindow, err := metrics.NewSlidingWindow(timeConfig)
	if err != nil {
		t.Fatalf("Failed to create time-based sliding window: %v", err)
	}
	defer timeWindow.Close()

	// Add values with small delays
	for i, value := range []float64{100, 200, 300} {
		if err := timeWindow.Add(value); err != nil {
			t.Errorf("Failed to add value %.2f: %v", value, err)
		}
		if i < 2 { // Don't sleep after last value
			time.Sleep(100 * time.Millisecond)
		}
	}

	// Verify time window has data
	if timeWindow.Size() == 0 {
		t.Error("Time window should have data")
	}

	t.Log("✅ Sliding window aggregation working correctly")
}

// TestTask39_TimeBucketingLogic tests the time-based bucketing (Subtask 39.4)
func TestTask39_TimeBucketingLogic(t *testing.T) {
	t.Log("Testing Task 39.4 - Time-Based Bucketing Logic")

	// Development-friendly configuration
	config := metrics.TimeBucketConfig{
		Granularity:        metrics.GranularitySecond,
		RetentionBuckets:   10, // Small retention for development
		MaxClockSkew:       1 * time.Second,
		EnableDownsampling: false,
		EnableUpsampling:   false,
		CleanupInterval:    2 * time.Second,
		EnableAsync:        false, // Synchronous for testing
		EnableCompression:  false,
		MaxMemoryUsage:     1024 * 1024, // 1MB limit
		FlushOnShutdown:    true,
	}

	manager, err := metrics.NewTimeBucketManager(config)
	if err != nil {
		t.Fatalf("Failed to create time bucket manager: %v", err)
	}

	if err := manager.Start(); err != nil {
		t.Fatalf("Failed to start time bucket manager: %v", err)
	}
	defer manager.Stop()

	// Add test entries
	now := time.Now()
	testEntries := []metrics.TimeBucketEntry{
		{
			Timestamp: now,
			Value:     100.0,
			MetricID:  "test_metric",
			Source:    "test",
			Tags:      map[string]string{"env": "test"},
		},
		{
			Timestamp: now.Add(500 * time.Millisecond),
			Value:     200.0,
			MetricID:  "test_metric",
			Source:    "test",
			Tags:      map[string]string{"env": "test"},
		},
	}

	for _, entry := range testEntries {
		if err := manager.AddEntry(entry); err != nil {
			t.Errorf("Failed to add entry: %v", err)
		}
	}

	// Verify bucket creation and data
	bucket, exists := manager.GetBucket(now)
	if !exists {
		t.Error("Expected bucket to exist for test timestamp")
	}

	if bucket.EntryCount() == 0 {
		t.Error("Expected bucket to have entries")
	}

	// Test aggregations
	agg, exists := bucket.GetAggregation("test_metric")
	if !exists {
		t.Error("Expected aggregation to exist for test_metric")
	}

	if agg.Count == 0 {
		t.Error("Expected aggregation to have count > 0")
	}

	t.Logf("✅ Time bucketing working: %d entries, count=%d, mean=%.2f", 
		bucket.EntryCount(), agg.Count, agg.Mean)
}

// TestTask39_MetricsIntegration tests the complete integration (Subtask 39.5)
func TestTask39_MetricsIntegration(t *testing.T) {
	t.Log("Testing Task 39.5 - Metrics Collection System Integration")

	// Development-friendly integration configuration
	config := metrics.IntegratorConfig{
		CollectionConfig: metrics.CollectionConfig{
			WorkerCount:     1,
			CollectionRate:  50 * time.Millisecond,
			ChannelBuffer:   10,
			BatchSize:      3,
			EnableBatching: false, // Disable for simplicity
			MaxRetries:     2,
			RetryDelay:     10 * time.Millisecond,
			EnableMetrics:  true,
			ShutdownTimeout: 1 * time.Second,
		},
		AggregationConfig: metrics.AggregationConfig{
			Interval:    100 * time.Millisecond,
			Type:        metrics.AggregationSlidingWindow,
			Percentile:  0.95,
			BufferSize:  20,
			EnableAsync: false,
			WindowConfig: metrics.SlidingWindowConfig{
				Type:        metrics.WindowTypeCount,
				Capacity:    5,
				EnableStats: true,
				EnableAsync: false,
			},
			BucketConfig: metrics.TimeBucketConfig{
				Granularity:        metrics.GranularitySecond,
				RetentionBuckets:   5,
				MaxClockSkew:       1 * time.Second,
				EnableDownsampling: false,
				EnableUpsampling:   false,
				CleanupInterval:    2 * time.Second,
				EnableAsync:        false,
				EnableCompression:  false,
				MaxMemoryUsage:     512 * 1024, // 512KB
				FlushOnShutdown:    true,
			},
			EnableAdvancedStats: true,
		},
		EnableEnhanced:   true,
		EnableCollection: false, // Focus on enhanced aggregation for this test
		EnableBasicAgg:   false,
	}

	integrator, err := metrics.NewMetricsIntegrator(config)
	if err != nil {
		t.Fatalf("Failed to create metrics integrator: %v", err)
	}

	if err := integrator.Start(); err != nil {
		t.Fatalf("Failed to start metrics integrator: %v", err)
	}
	defer integrator.Stop()

	// Test adding metric values
	tags := map[string]string{"test": "integration"}
	testValues := []float64{10, 20, 30, 40, 50}

	for _, value := range testValues {
		if err := integrator.AddMetricValue("integration_test", value, tags); err != nil {
			t.Errorf("Failed to add metric value %.2f: %v", value, err)
		}
	}

	// Give time for processing
	time.Sleep(200 * time.Millisecond)

	// Test retrieving statistics
	stats, err := integrator.GetSlidingWindowStats("integration_test")
	if err != nil {
		t.Errorf("Failed to get sliding window stats: %v", err)
	} else {
		if stats.Count == 0 {
			t.Error("Expected sliding window to have data")
		}
		t.Logf("Integration stats: Count=%d, Mean=%.2f", stats.Count, stats.Mean)
	}

	// Test statistical summary
	summary, err := integrator.GetStatisticalSummary("integration_test")
	if err != nil {
		t.Errorf("Failed to get statistical summary: %v", err)
	} else {
		if summary.Count == 0 {
			t.Error("Expected statistical summary to have data")
		}
		t.Logf("Summary: Count=%d, Mean=%.2f, Min=%.2f, Max=%.2f", 
			summary.Count, summary.Mean, summary.Min, summary.Max)
	}

	t.Log("✅ Metrics integration working correctly")
}

// TestTask39_DevelopmentResourceUsage tests that the system is development-friendly
func TestTask39_DevelopmentResourceUsage(t *testing.T) {
	t.Log("Testing Task 39.6 - Development-Appropriate Resource Usage")

	// This test ensures the system doesn't overwhelm development hardware
	startTime := time.Now()

	// Create a small-scale system
	config := metrics.IntegratorConfig{
		AggregationConfig: metrics.AggregationConfig{
			WindowConfig: metrics.SlidingWindowConfig{
				Type:        metrics.WindowTypeCount,
				Capacity:    10, // Very small
				EnableStats: true,
				EnableAsync: false,
			},
			BucketConfig: metrics.TimeBucketConfig{
				Granularity:      metrics.GranularitySecond,
				RetentionBuckets: 5, // Very small
				MaxMemoryUsage:   100 * 1024, // 100KB limit
			},
			EnableAdvancedStats: true,
		},
		EnableEnhanced:   true,
		EnableCollection: false,
		EnableBasicAgg:   false,
	}

	integrator, err := metrics.NewMetricsIntegrator(config)
	if err != nil {
		t.Fatalf("Failed to create metrics integrator: %v", err)
	}

	if err := integrator.Start(); err != nil {
		t.Fatalf("Failed to start metrics integrator: %v", err)
	}
	defer integrator.Stop()

	// Add a reasonable amount of data for development testing
	for i := 0; i < 20; i++ { // Small dataset
		if err := integrator.AddMetricValue("resource_test", float64(i), nil); err != nil {
			t.Errorf("Failed to add metric value: %v", err)
		}
	}

	// Verify the test completes quickly (development-appropriate)
	elapsed := time.Since(startTime)
	if elapsed > 2*time.Second {
		t.Errorf("Test took too long: %v (should be < 2s for development)", elapsed)
	}

	// Verify we can get results
	allMetrics := integrator.GetAllMetrics()
	if len(allMetrics) == 0 {
		t.Error("Expected to track some metrics")
	}

	t.Logf("✅ Resource usage test completed in %v with %d metrics tracked", 
		elapsed, len(allMetrics))
	t.Log("✅ System is development-hardware friendly")
}
